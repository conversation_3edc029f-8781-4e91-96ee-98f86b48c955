from django.urls import path, re_path
from .views import IndexView, ChatAPIView, ChatAPIViewDRF, KLineAPIView, AIAnalysisAPIView, HistoricalAnalysisAPIView
from .quotation_views import QuotationProxyView

urlpatterns = [
    # 首页
    path('', IndexView.as_view(), name='ai_index'),
    
    # 原生Django视图API - 与Flask完全兼容的端点
    path('chat/', ChatAPIView.as_view(), name='ai_chat'),
    
    # DRF API - 可选的REST框架端点
    path('chat/drf/', ChatAPIViewDRF.as_view(), name='ai_chat_drf'),
    
    # 新增：K线数据API
    path('kline/', KLineAPIView.as_view(), name='kline_api'),
    
    # 新增：AI分析API
    path('analysis/', AIAnalysisAPIView.as_view(), name='ai_analysis_api'),

    # 新增：历史同期分析API
    path('historical-analysis/', HistoricalAnalysisAPIView.as_view(), name='historical_analysis_api'),

    # 行情API代理 - 转发到币安API
    # 使用re_path捕获所有子路径并传递给代理视图
    re_path(r'^quotation/(?P<path>.*)$', QuotationProxyView.as_view(), name='quotation_proxy'),
] 
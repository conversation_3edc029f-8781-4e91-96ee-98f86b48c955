"""
历史数据服务层
处理COS数据读取、技术指标计算、AI分析等
"""

import io
import json
import zipfile
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from decimal import Decimal
import boto3
from botocore.config import Config
from django.conf import settings
from django.utils import timezone
from typing import List, Dict, Optional, Tuple
import logging

from .models import HistoricalKlineData, TechnicalIndicatorData, AIAnalysisResult
from .technical_indicators_service import TechnicalIndicatorService

logger = logging.getLogger(__name__)


class COSDataService:
    """腾讯云COS数据服务"""
    
    def __init__(self):
        """初始化COS客户端"""
        config = Config(
            region_name='ap-guangzhou',
            s3={'addressing_style': 'virtual'},
            connect_timeout=60,  # 连接超时60秒
            read_timeout=300,    # 读取超时5分钟（支持69MB文件下载）
        )
        
        self.cos_client = boto3.client(
            's3',
            endpoint_url='https://cos.ap-guangzhou.myqcloud.com',
            aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
            config=config
        )
        
        self.bucket_name = settings.AWS_STORAGE_BUCKET_NAME
    
    def download_year_data_filtered(self, symbol: str, interval: str, year: int,
                                   target_date: str = None, days_range: int = 3) -> pd.DataFrame:
        """
        下载年度数据并立即过滤指定日期范围

        参数:
        - symbol: 交易对符号 (如 BTCUSDT)
        - interval: 时间间隔 (如 1m)
        - year: 年份 (如 2024)
        - target_date: 目标日期 (如 '2024-01-15')，为None时返回全年数据
        - days_range: 前后天数范围

        返回:
        - 过滤后的pandas DataFrame
        """
        try:
            # 构建COS路径 - 修复为正确的4层路径
            cos_key = f"finance-1324685443/finance-1324685443/finance-1324685443/crypto-kline-data-v2/20250724/{symbol}/{interval}/{symbol}_{interval}_{year}_compressed.json"

            logger.info(f"下载COS年度数据: {cos_key}")
            start_time = datetime.now()

            # 从COS下载文件
            response = self.cos_client.get_object(Bucket=self.bucket_name, Key=cos_key)

            # 处理HTTP响应格式的文件
            content = response['Body'].read()

            try:
                # 解码内容
                content_str = content.decode('utf-8')

                # 处理HTTP响应格式
                lines = content_str.split('\n', 2)  # 只分割前2行

                if len(lines) >= 2 and lines[0].strip().isdigit():
                    # HTTP响应格式：第一行是Content-Length，第二行是JSON
                    json_line = lines[1].strip()
                else:
                    # 直接是JSON格式
                    json_line = content_str.strip()

                # 移除可能的回车符
                json_line = json_line.replace('\r', '')

                # 解析JSON
                logger.info(f"开始解析JSON数据，长度: {len(json_line):,}字符")
                data = json.loads(json_line)
                logger.info("JSON解析成功")

            except Exception as e:
                logger.error(f"JSON解析失败: {e}")
                # 尝试修复常见的JSON问题
                try:
                    # 移除可能的控制字符
                    import re
                    content_str = content.decode('utf-8', errors='ignore')
                    lines = content_str.strip().split('\n')

                    # 找到JSON行
                    json_line = None
                    for line in lines:
                        clean_line = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', line.strip())
                        if clean_line.startswith('{') and 'metadata' in clean_line:
                            json_line = clean_line
                            break

                    if json_line:
                        data = json.loads(json_line)
                    else:
                        logger.error("无法找到有效的JSON数据")
                        return pd.DataFrame()

                except Exception as e2:
                    logger.error(f"JSON修复解析也失败: {e2}")
                    return pd.DataFrame()
            download_time = datetime.now() - start_time
            logger.info(f"COS下载完成，耗时: {download_time.total_seconds():.1f}秒")

            # 转换为DataFrame
            if 'data' in data and data['data']:
                df = pd.DataFrame(data['data'])

                # 确保列名正确
                if 'timestamp' in df.columns:
                    # 币安数据使用毫秒时间戳
                    df['open_time'] = pd.to_datetime(df['timestamp'], unit='ms')
                    # 重命名列以匹配预期格式
                    column_mapping = {
                        'timestamp': 'timestamp',
                        'open': 'open',
                        'high': 'high',
                        'low': 'low',
                        'close': 'close',
                        'volume': 'volume'
                    }
                    df = df.rename(columns=column_mapping)

                # 转换数值类型
                for col in ['open', 'high', 'low', 'close', 'volume']:
                    if col in df.columns:
                        df[col] = pd.to_numeric(df[col], errors='coerce')

                logger.info(f"原始数据: {len(df):,}条记录")

                # 如果指定了目标日期，立即过滤
                if target_date:
                    df = self._filter_date_range(df, target_date, days_range)
                    logger.info(f"过滤后数据: {len(df):,}条记录")

                total_time = datetime.now() - start_time
                logger.info(f"数据处理完成，总耗时: {total_time.total_seconds():.1f}秒")

                return df
            else:
                logger.warning(f"JSON文件中无数据: {cos_key}")
                return pd.DataFrame()

        except Exception as e:
            logger.error(f"下载年度数据失败 {symbol} {year}: {e}")
            return pd.DataFrame()

    def _filter_date_range(self, df: pd.DataFrame, target_date: str, days_range: int) -> pd.DataFrame:
        """过滤指定日期范围的数据"""
        try:
            if df.empty or 'open_time' not in df.columns:
                return df

            # 解析目标日期
            target_dt = datetime.strptime(target_date, '%Y-%m-%d')
            start_date = target_dt - timedelta(days=days_range)
            end_date = target_dt + timedelta(days=days_range)

            # 过滤日期范围
            mask = (df['open_time'].dt.date >= start_date.date()) & \
                   (df['open_time'].dt.date <= end_date.date())

            filtered_df = df[mask].copy()

            # 按时间排序
            if not filtered_df.empty:
                filtered_df = filtered_df.sort_values('open_time').reset_index(drop=True)

            return filtered_df

        except Exception as e:
            logger.error(f"过滤日期范围失败: {e}")
            return df

    def download_zip_data(self, symbol: str, interval: str, date_str: str) -> pd.DataFrame:
        """
        从COS下载并解析ZIP数据文件

        参数:
        - symbol: 交易对符号 (如 BTCUSDT)
        - interval: 时间间隔 (如 15m, 1d)
        - date_str: 日期字符串 (如 2024-01)

        返回:
        - pandas DataFrame
        """
        try:
            # 根据年份确定路径结构
            year = int(date_str.split('-')[0])

            if year == 2025:
                # 2025年数据：按日存储，需要具体日期
                # 这里暂时返回空，因为需要具体的日期信息
                logger.warning(f"2025年数据需要具体日期，当前只有年份: {date_str}")
                return pd.DataFrame()
            else:
                # 历史数据（2017-2024）：按年存储
                cos_key = f"finance-1324685443/finance-1324685443/finance-1324685443/crypto-kline-data-v2/20250724/{symbol}/{interval}/{symbol}_{interval}_{year}_compressed.json"

            logger.info(f"下载COS数据: {cos_key}")

            # 从COS下载文件
            response = self.cos_client.get_object(Bucket=self.bucket_name, Key=cos_key)

            # 根据文件类型处理
            if cos_key.endswith('.json'):
                # JSON文件直接解析
                json_content = response['Body'].read().decode('utf-8')
                data = json.loads(json_content)

                # 转换为DataFrame
                if 'data' in data and data['data']:
                    df = pd.DataFrame(data['data'])
                    # 确保列名正确
                    if len(df.columns) >= 6:
                        df.columns = ['open_time', 'open', 'high', 'low', 'close', 'volume'] + list(df.columns[6:])
                        # 转换时间戳
                        df['open_time'] = pd.to_datetime(df['open_time'], unit='ms')
                        # 转换数值类型
                        for col in ['open', 'high', 'low', 'close', 'volume']:
                            if col in df.columns:
                                df[col] = pd.to_numeric(df[col], errors='coerce')
                    return df
                else:
                    logger.warning(f"JSON文件中无数据: {cos_key}")
                    return pd.DataFrame()
            else:
                # ZIP文件处理（保持原有逻辑）
                zip_content = response['Body'].read()
            
            # 解析ZIP文件
            with zipfile.ZipFile(io.BytesIO(zip_content), 'r') as zip_file:
                csv_files = [f for f in zip_file.namelist() if f.endswith('.csv')]
                
                if not csv_files:
                    logger.warning(f"ZIP文件中未找到CSV: {cos_key}")
                    return pd.DataFrame()
                
                # 读取第一个CSV文件
                with zip_file.open(csv_files[0]) as csv_file:
                    df = pd.read_csv(csv_file, header=None)
                    
                    # 设置列名
                    df.columns = [
                        'open_time', 'open', 'high', 'low', 'close', 'volume',
                        'close_time', 'quote_volume', 'trades_count', 
                        'taker_buy_base_volume', 'taker_buy_quote_volume', 'ignore'
                    ]
                    
                    # 转换时间戳
                    df['open_time'] = pd.to_datetime(df['open_time'], unit='ms')
                    df['close_time'] = pd.to_datetime(df['close_time'], unit='ms')
                    
                    # 转换数值类型
                    numeric_cols = ['open', 'high', 'low', 'close', 'volume', 'quote_volume']
                    for col in numeric_cols:
                        df[col] = pd.to_numeric(df[col], errors='coerce')
                    
                    df['trades_count'] = pd.to_numeric(df['trades_count'], errors='coerce')
                    
                    return df[['open_time', 'close_time', 'open', 'high', 'low', 'close', 'volume', 'quote_volume', 'trades_count']]
                    
        except Exception as e:
            logger.error(f"下载COS数据失败 {cos_key}: {e}")
            return pd.DataFrame()
    
    def get_available_dates(self, symbol: str, interval: str) -> List[str]:
        """获取可用的数据日期列表"""
        try:
            dates = []

            # 检查2025年数据
            prefix_2025 = f"crypto-kline-data-v2/20250726/{symbol}/{interval}/"
            try:
                response = self.cos_client.list_objects_v2(
                    Bucket=self.bucket_name,
                    Prefix=prefix_2025
                )

                if 'Contents' in response:
                    for obj in response['Contents']:
                        key = obj['Key']
                        if key.endswith('.json'):
                            # 提取年份 (如: BTCUSDT_1m_2025_compressed.json -> 2025)
                            filename = key.split('/')[-1]
                            if '_2025_' in filename:
                                dates.append('2025-01')  # 2025年数据
            except Exception as e:
                logger.debug(f"2025年数据检查失败: {e}")

            # 检查历史数据（2017-2024）
            prefix_historical = f"finance-1324685443/finance-1324685443/finance-1324685443/crypto-kline-data-v2/20250724/{symbol}/{interval}/"
            try:
                response = self.cos_client.list_objects_v2(
                    Bucket=self.bucket_name,
                    Prefix=prefix_historical
                )

                if 'Contents' in response:
                    for obj in response['Contents']:
                        key = obj['Key']
                        if key.endswith('.json'):
                            # 提取年份 (如: BTCUSDT_1m_2024_compressed.json -> 2024-01)
                            filename = key.split('/')[-1]
                            for year in range(2017, 2025):
                                if f'_{year}_' in filename:
                                    dates.append(f'{year}-01')
                                    break
            except Exception as e:
                logger.debug(f"历史数据检查失败: {e}")

            return sorted(set(dates))

        except Exception as e:
            logger.error(f"获取可用日期失败 {symbol} {interval}: {e}")
            return []


class TechnicalIndicatorService:
    """技术指标计算服务"""
    
    @staticmethod
    def calculate_ema(data: np.ndarray, period: int) -> np.ndarray:
        """计算EMA"""
        alpha = 2.0 / (period + 1.0)
        ema_values = np.zeros(len(data))
        ema_values[0] = data[0]
        
        for i in range(1, len(data)):
            ema_values[i] = alpha * data[i] + (1 - alpha) * ema_values[i-1]
        
        return ema_values
    
    @classmethod
    def calculate_macd(cls, prices: np.ndarray, fast=12, slow=26, signal=9) -> Tuple[float, float, float]:
        """计算MACD"""
        if len(prices) < slow:
            return 0.0, 0.0, 0.0
        
        ema_fast = cls.calculate_ema(prices, fast)
        ema_slow = cls.calculate_ema(prices, slow)
        macd_line = ema_fast - ema_slow
        signal_line = cls.calculate_ema(macd_line, signal)
        histogram = macd_line - signal_line
        
        return float(macd_line[-1]), float(signal_line[-1]), float(histogram[-1])
    
    @staticmethod
    def calculate_rsi(prices: np.ndarray, period: int = 14) -> float:
        """计算RSI"""
        if len(prices) < period + 1:
            return 50.0
        
        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)
        
        avg_gain = np.mean(gains[:period])
        avg_loss = np.mean(losses[:period])
        
        for i in range(period, len(gains)):
            avg_gain = (avg_gain * (period - 1) + gains[i]) / period
            avg_loss = (avg_loss * (period - 1) + losses[i]) / period
        
        if avg_loss == 0:
            return 100.0
        
        rs = avg_gain / avg_loss
        return 100.0 - (100.0 / (1.0 + rs))
    
    @staticmethod
    def calculate_moving_average(prices: np.ndarray, period: int) -> float:
        """计算移动平均"""
        if len(prices) < period:
            return float(prices[-1]) if len(prices) > 0 else 0.0
        return float(np.mean(prices[-period:]))


class HistoricalDataService:
    """历史数据综合服务"""
    
    def __init__(self):
        self.cos_service = COSDataService()
        self.indicator_service = TechnicalIndicatorService()
    
    def load_recent_data(self, symbol: str, interval: str = "15m", limit: int = 200) -> pd.DataFrame:
        """
        加载最近的历史数据
        
        参数:
        - symbol: 交易对
        - interval: 时间间隔
        - limit: 数据条数
        
        返回:
        - 合并后的DataFrame
        """
        try:
            # 获取可用日期
            available_dates = self.cos_service.get_available_dates(symbol, interval)
            
            if not available_dates:
                logger.warning(f"未找到 {symbol} {interval} 的数据")
                return pd.DataFrame()
            
            # 从最新日期开始加载数据
            all_data = []
            
            for date_str in reversed(available_dates[-3:]):  # 加载最近3个月
                df = self.cos_service.download_zip_data(symbol, interval, date_str)
                if not df.empty:
                    all_data.append(df)
                
                # 如果数据足够就停止
                total_rows = sum(len(d) for d in all_data)
                if total_rows >= limit:
                    break
            
            if not all_data:
                return pd.DataFrame()
            
            # 合并数据
            combined_df = pd.concat(all_data, ignore_index=True)
            combined_df = combined_df.drop_duplicates(subset=['open_time'])
            combined_df = combined_df.sort_values('open_time')
            
            # 限制数据量
            if len(combined_df) > limit:
                combined_df = combined_df.tail(limit)
            
            logger.info(f"加载 {symbol} {interval} 数据: {len(combined_df)} 条")
            return combined_df
            
        except Exception as e:
            logger.error(f"加载历史数据失败 {symbol} {interval}: {e}")
            return pd.DataFrame()
    
    def calculate_technical_indicators(self, df: pd.DataFrame) -> Dict:
        """计算技术指标 - 使用pandas-ta"""
        if df.empty or len(df) < 50:
            return {}
        
        try:
            # 准备DataFrame格式
            indicator_df = self.indicator_service.prepare_dataframe(df.to_dict('records'))
            
            # 计算所有指标
            indicators = self.indicator_service.calculate_all_indicators(indicator_df)
            
            return indicators
            
        except Exception as e:
            logger.error(f"计算技术指标失败: {e}")
            return {}
    
    def get_chart_data(self, symbol: str, interval: str = "15m", period: str = "1d") -> Dict:
        """
        获取图表数据
        
        参数:
        - symbol: 交易对
        - interval: 时间间隔
        - period: 时间范围 (1d, 1w, 1m, 3m, 6m, 1y)
        
        返回:
        - 图表数据字典
        """
        # 根据period确定数据量
        period_limits = {
            '1d': 96,      # 1天 = 96个15分钟
            '1w': 672,     # 1周 = 672个15分钟
            '1m': 2880,    # 1月 = 2880个15分钟
            '3m': 8640,    # 3月
            '6m': 17280,   # 6月
            '1y': 35040,   # 1年
        }
        
        limit = period_limits.get(period, 672)
        
        # 加载数据
        df = self.load_recent_data(symbol, interval, limit)
        
        if df.empty:
            return {"error": "未找到数据"}
        
        # 计算技术指标
        indicators = self.calculate_technical_indicators(df)
        
        # 准备图表数据
        chart_data = {
            "symbol": symbol,
            "interval": interval,
            "period": period,
            "data_points": len(df),
            "klines": [],
            "indicators": indicators,
            "volume": []
        }
        
        # K线数据
        for _, row in df.iterrows():
            chart_data["klines"].append({
                "timestamp": row['open_time'].isoformat(),
                "open": float(row['open']),
                "high": float(row['high']),
                "low": float(row['low']),
                "close": float(row['close']),
            })
            
            chart_data["volume"].append({
                "timestamp": row['open_time'].isoformat(),
                "volume": float(row['volume'])
            })
        
        return chart_data
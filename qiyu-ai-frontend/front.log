
> qiyu-ai-frontend@0.1.0 start
> react-scripts start

(node:60351) [DEP_WEBPACK_DEV_SERVER_ON_AFTER_SETUP_MIDDLEWARE] DeprecationWarning: 'onAfterSetupMiddleware' option is deprecated. Please use the 'setupMiddlewares' option.
(Use `node --trace-deprecation ...` to show where the warning was created)
(node:60351) [DEP_WEBPACK_DEV_SERVER_ON_BEFORE_SETUP_MIDDLEWARE] DeprecationWarning: 'onBeforeSetupMiddleware' option is deprecated. Please use the 'setupMiddlewares' option.
Starting the development server...

Compiled with warnings.

[eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Dashboard/Dashboard.advanced.js
  Line 130:9:  'getPredictionSymbols' is assigned a value but never used  no-unused-vars

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Dashboard/Dashboard.advanced.js
  Line 130:9:  'getPredictionSymbols' is assigned a value but never used  no-unused-vars

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/components/Auth/AuthContainer.js
  Line 8:10:  'inviteCode' is assigned a value but never used  no-unused-vars

src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/components/Auth/AuthContainer.js
  Line 8:10:  'inviteCode' is assigned a value but never used  no-unused-vars

src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Failed to compile.

[eslint] 
src/lib/smsAuth.js
  Line 236:90:  'inviteCode' is not defined  no-undef

Search for the keywords to learn more about each error.
WARNING in [eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

ERROR in [eslint] 
src/lib/smsAuth.js
  Line 236:90:  'inviteCode' is not defined  no-undef

Search for the keywords to learn more about each error.

webpack compiled with 1 error and 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Failed to compile.

[eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 128:11:  'realTimeDataManager' is not defined      no-undef
  Line 432:38:  'binanceWebSocketService' is not defined  no-undef
  Line 438:17:  'binanceWebSocketService' is not defined  no-undef

Search for the keywords to learn more about each error.
WARNING in [eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 176:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 467:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 518:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

ERROR in [eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 128:11:  'realTimeDataManager' is not defined      no-undef
  Line 432:38:  'binanceWebSocketService' is not defined  no-undef
  Line 438:17:  'binanceWebSocketService' is not defined  no-undef

Search for the keywords to learn more about each error.

webpack compiled with 1 error and 1 warning
Compiling...
Failed to compile.

[eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 428:38:  'binanceWebSocketService' is not defined  no-undef
  Line 434:17:  'binanceWebSocketService' is not defined  no-undef

Search for the keywords to learn more about each error.
WARNING in [eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                                                                  no-unused-vars
  Line 172:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                                                                      react-hooks/exhaustive-deps
  Line 463:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'enableRealTime', 'handleRealTimeUpdate', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 514:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                                                                       react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

ERROR in [eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 428:38:  'binanceWebSocketService' is not defined  no-undef
  Line 434:17:  'binanceWebSocketService' is not defined  no-undef

Search for the keywords to learn more about each error.

webpack compiled with 1 error and 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                        no-unused-vars
  Line 24:29:  'setRealTimeConnected' is assigned a value but never used                                                                                                                                                                                                                                 no-unused-vars
  Line 62:9:   'handleRealTimeUpdate' is assigned a value but never used                                                                                                                                                                                                                                 no-unused-vars
  Line 172:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                            react-hooks/exhaustive-deps
  Line 451:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 502:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                             react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                        no-unused-vars
  Line 24:29:  'setRealTimeConnected' is assigned a value but never used                                                                                                                                                                                                                                 no-unused-vars
  Line 62:9:   'handleRealTimeUpdate' is assigned a value but never used                                                                                                                                                                                                                                 no-unused-vars
  Line 172:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                            react-hooks/exhaustive-deps
  Line 451:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 502:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                             react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                        no-unused-vars
  Line 24:29:  'setRealTimeConnected' is assigned a value but never used                                                                                                                                                                                                                                 no-unused-vars
  Line 62:9:   'handleRealTimeUpdate' is assigned a value but never used                                                                                                                                                                                                                                 no-unused-vars
  Line 172:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                            react-hooks/exhaustive-deps
  Line 451:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 502:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                             react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                        no-unused-vars
  Line 24:29:  'setRealTimeConnected' is assigned a value but never used                                                                                                                                                                                                                                 no-unused-vars
  Line 62:9:   'handleRealTimeUpdate' is assigned a value but never used                                                                                                                                                                                                                                 no-unused-vars
  Line 172:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                            react-hooks/exhaustive-deps
  Line 451:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 502:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                             react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                        no-unused-vars
  Line 24:29:  'setRealTimeConnected' is assigned a value but never used                                                                                                                                                                                                                                 no-unused-vars
  Line 62:9:   'handleRealTimeUpdate' is assigned a value but never used                                                                                                                                                                                                                                 no-unused-vars
  Line 172:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                            react-hooks/exhaustive-deps
  Line 451:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 502:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                             react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                        no-unused-vars
  Line 24:29:  'setRealTimeConnected' is assigned a value but never used                                                                                                                                                                                                                                 no-unused-vars
  Line 62:9:   'handleRealTimeUpdate' is assigned a value but never used                                                                                                                                                                                                                                 no-unused-vars
  Line 172:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                            react-hooks/exhaustive-deps
  Line 451:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 502:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                             react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                        no-unused-vars
  Line 24:29:  'setRealTimeConnected' is assigned a value but never used                                                                                                                                                                                                                                 no-unused-vars
  Line 62:9:   'handleRealTimeUpdate' is assigned a value but never used                                                                                                                                                                                                                                 no-unused-vars
  Line 172:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                            react-hooks/exhaustive-deps
  Line 451:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 502:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                             react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

src/utils/debugHelper.js
  Line 11:7:  'streamService' is assigned a value but never used  no-unused-vars

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                        no-unused-vars
  Line 24:29:  'setRealTimeConnected' is assigned a value but never used                                                                                                                                                                                                                                 no-unused-vars
  Line 62:9:   'handleRealTimeUpdate' is assigned a value but never used                                                                                                                                                                                                                                 no-unused-vars
  Line 172:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                            react-hooks/exhaustive-deps
  Line 451:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 502:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                             react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

src/utils/debugHelper.js
  Line 11:7:  'streamService' is assigned a value but never used  no-unused-vars

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                        no-unused-vars
  Line 24:29:  'setRealTimeConnected' is assigned a value but never used                                                                                                                                                                                                                                 no-unused-vars
  Line 62:9:   'handleRealTimeUpdate' is assigned a value but never used                                                                                                                                                                                                                                 no-unused-vars
  Line 172:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                            react-hooks/exhaustive-deps
  Line 451:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 502:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                             react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

src/utils/debugHelper.js
  Line 178:11:  Duplicate key 'refreshCharts'  no-dupe-keys

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                        no-unused-vars
  Line 24:29:  'setRealTimeConnected' is assigned a value but never used                                                                                                                                                                                                                                 no-unused-vars
  Line 62:9:   'handleRealTimeUpdate' is assigned a value but never used                                                                                                                                                                                                                                 no-unused-vars
  Line 172:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                            react-hooks/exhaustive-deps
  Line 451:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 502:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                             react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

src/utils/debugHelper.js
  Line 178:11:  Duplicate key 'refreshCharts'  no-dupe-keys

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                        no-unused-vars
  Line 24:29:  'setRealTimeConnected' is assigned a value but never used                                                                                                                                                                                                                                 no-unused-vars
  Line 62:9:   'handleRealTimeUpdate' is assigned a value but never used                                                                                                                                                                                                                                 no-unused-vars
  Line 172:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                            react-hooks/exhaustive-deps
  Line 451:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 502:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                             react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

src/utils/debugHelper.js
  Line 178:11:  Duplicate key 'refreshCharts'  no-dupe-keys

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                        no-unused-vars
  Line 24:29:  'setRealTimeConnected' is assigned a value but never used                                                                                                                                                                                                                                 no-unused-vars
  Line 62:9:   'handleRealTimeUpdate' is assigned a value but never used                                                                                                                                                                                                                                 no-unused-vars
  Line 172:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                            react-hooks/exhaustive-deps
  Line 451:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 502:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                             react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

src/utils/debugHelper.js
  Line 178:11:  Duplicate key 'refreshCharts'  no-dupe-keys

webpack compiled with 1 warning
Compiling...
Compiled with warnings.

[eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                        no-unused-vars
  Line 24:29:  'setRealTimeConnected' is assigned a value but never used                                                                                                                                                                                                                                 no-unused-vars
  Line 62:9:   'handleRealTimeUpdate' is assigned a value but never used                                                                                                                                                                                                                                 no-unused-vars
  Line 172:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                            react-hooks/exhaustive-deps
  Line 451:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 502:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                             react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

src/utils/debugHelper.js
  Line 219:11:  Duplicate key 'refreshCharts'  no-dupe-keys

Search for the keywords to learn more about each warning.
To ignore, add // eslint-disable-next-line to the line before.

WARNING in [eslint] 
src/components/Chart/EnhancedKLineChart.js
  Line 3:25:   'Select' is defined but never used                                                                                                                                                                                                                                                        no-unused-vars
  Line 24:29:  'setRealTimeConnected' is assigned a value but never used                                                                                                                                                                                                                                 no-unused-vars
  Line 62:9:   'handleRealTimeUpdate' is assigned a value but never used                                                                                                                                                                                                                                 no-unused-vars
  Line 172:6:  React Hook useCallback has a missing dependency: 'timeframe'. Either include it or remove the dependency array                                                                                                                                                                            react-hooks/exhaustive-deps
  Line 451:6:  React Hook useEffect has missing dependencies: 'darkThemeConfig', 'initializeIndicators', and 'onDataUpdate'. Either include them or remove the dependency array. If 'onDataUpdate' changes too often, find the parent component that defines it and wrap that definition in useCallback  react-hooks/exhaustive-deps
  Line 502:6:  React Hook useCallback has missing dependencies: 'symbol' and 'timeframe'. Either include them or remove the dependency array                                                                                                                                                             react-hooks/exhaustive-deps

src/components/Chart/GridKLineChart.js
  Line 2:16:    'Space' is defined but never used                                                                                                         no-unused-vars
  Line 2:23:    'Button' is defined but never used                                                                                                        no-unused-vars
  Line 2:31:    'Radio' is defined but never used                                                                                                         no-unused-vars
  Line 3:10:    'ReloadOutlined' is defined but never used                                                                                                no-unused-vars
  Line 3:26:    'FullscreenOutlined' is defined but never used                                                                                            no-unused-vars
  Line 3:46:    'ClockCircleOutlined' is defined but never used                                                                                           no-unused-vars
  Line 19:10:   'realTimeConnected' is assigned a value but never used                                                                                    no-unused-vars
  Line 20:10:   'loadedCharts' is assigned a value but never used                                                                                         no-unused-vars
  Line 69:9:    'handleTimeframeChange' is assigned a value but never used                                                                                no-unused-vars
  Line 86:9:    'refreshData' is assigned a value but never used                                                                                          no-unused-vars
  Line 99:9:    'toggleFullscreen' is assigned a value but never used                                                                                     no-unused-vars
  Line 160:6:   React Hook useEffect has a missing dependency: 'sortedYears'. Either include it or remove the dependency array                            react-hooks/exhaustive-deps
  Line 160:7:   React Hook useEffect has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked  react-hooks/exhaustive-deps
  Line 186:19:  'isVisible' is assigned a value but never used                                                                                            no-unused-vars

src/components/Dashboard/AIPredictionModule.js
  Line 2:16:   'Row' is defined but never used             no-unused-vars
  Line 2:21:   'Col' is defined but never used             no-unused-vars
  Line 17:15:  'Title' is assigned a value but never used  no-unused-vars

src/components/Layout/Nav.js
  Line 4:8:  'Atropos' is defined but never used  no-unused-vars

src/config/yearConfig.js
  Line 121:1:  Assign object to a variable before exporting as module default  import/no-anonymous-default-export

src/pages/Profile/Profile.js
  Line 2:43:   'Avatar' is defined but never used                                                                                      no-unused-vars
  Line 2:51:   'Space' is defined but never used                                                                                       no-unused-vars
  Line 2:81:   'Descriptions' is defined but never used                                                                                no-unused-vars
  Line 4:3:    'UserOutlined' is defined but never used                                                                                no-unused-vars
  Line 6:3:    'ShareAltOutlined' is defined but never used                                                                            no-unused-vars
  Line 7:3:    'PhoneOutlined' is defined but never used                                                                               no-unused-vars
  Line 8:3:    'SafetyOutlined' is defined but never used                                                                              no-unused-vars
  Line 21:9:   'TextArea' is assigned a value but never used                                                                           no-unused-vars
  Line 26:10:  'agentStats' is assigned a value but never used                                                                         no-unused-vars
  Line 86:6:   React Hook useEffect has a missing dependency: 'transactions.length'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

src/services/CryptoMetadataService.js
  Line 1:31:  'YEAR_CONFIG' is defined but never used  no-unused-vars

src/services/RealTimeDataManager.js
  Line 371:13:  'formattedData' is assigned a value but never used  no-unused-vars

src/utils/debugHelper.js
  Line 219:11:  Duplicate key 'refreshCharts'  no-dupe-keys

webpack compiled with 1 warning

/**
 * AI分析服务 - 与后端AI API交互
 * 基于现有的小程序AI分析逻辑，适配Web前端
 */

class AIAnalysisService {
  constructor() {
    this.baseURL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000';
    this.aiEndpoint = '/api/ai/chat/';
    this.quotationEndpoint = '/api/ai/quotation';
    
    // 分析类型配置
    this.analysisTypes = {
      'quick': { label: '快速分析', period: '未来24小时' },
      'four_hour': { label: '闪电分析', period: '未来4小时' },
      'one_day': { label: '一日分析', period: '未来24小时' },
      'three_day': { label: '三日分析', period: '未来3天' },
      'week': { label: '一周分析', period: '未来一周' },
      'long_term': { label: '长期分析', period: '长期' }
    };
    
    // 币种中文名称映射
    this.coinNameMap = {
      'BTC': '比特币',
      'ETH': '以太坊',
      'BNB': '币安币',
      'SOL': '索拉纳',
      'ADA': '卡尔达诺',
      'XRP': '瑞波币',
      'DOGE': '狗狗币',
      'DOT': '波卡',
      'MATIC': '马蒂奇',
      'LTC': '莱特币'
    };
  }

  /**
   * 获取K线数据
   */
  async fetchKlineData(symbol, analysisType) {
    try {
      const intervals = this.getIntervalsForAnalysis(analysisType);
      const klineData = {};
      
      // 获取日K线数据（90天）
      const dailyData = await this.fetchBinanceKlines(symbol, '1d', 90);
      klineData.dailyKlines = dailyData;
      
      // 根据分析类型获取短期K线数据
      if (intervals.shortTerm) {
        const shortTermData = await this.fetchBinanceKlines(
          symbol, 
          intervals.shortTerm.interval, 
          intervals.shortTerm.limit
        );
        klineData.shortTermKlines = shortTermData;
      }
      
      return klineData;
    } catch (error) {
      console.error('获取K线数据失败:', error);
      throw error;
    }
  }

  /**
   * 获取实时价格数据
   */
  async fetchTickerData(symbol) {
    try {
      const response = await fetch(
        `${this.baseURL}${this.quotationEndpoint}/binance/api/v3/ticker/24hr?symbol=${symbol}USDT`
      );
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      return {
        symbol: data.symbol,
        lastPrice: data.lastPrice,
        priceChangePercent: data.priceChangePercent,
        volume: data.volume,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('获取实时价格失败:', error);
      throw error;
    }
  }

  /**
   * 获取订单簿数据
   */
  async fetchOrderbookData(symbol, limit = 20) {
    try {
      const response = await fetch(
        `${this.baseURL}${this.quotationEndpoint}/binance/api/v3/depth?symbol=${symbol}USDT&limit=${limit}`
      );
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('获取订单簿数据失败:', error);
      return null;
    }
  }

  /**
   * 获取币安K线数据
   */
  async fetchBinanceKlines(symbol, interval, limit) {
    try {
      const response = await fetch(
        `${this.baseURL}${this.quotationEndpoint}/binance/api/v3/klines?symbol=${symbol}USDT&interval=${interval}&limit=${limit}`
      );
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('获取K线数据失败:', error);
      throw error;
    }
  }

  /**
   * 根据分析类型获取时间间隔配置
   */
  getIntervalsForAnalysis(analysisType) {
    const configs = {
      'four_hour': { shortTerm: { interval: '1m', limit: 240 } }, // 4小时1分钟数据
      'one_day': { shortTerm: { interval: '5m', limit: 288 } },   // 24小时5分钟数据
      'three_day': { shortTerm: { interval: '15m', limit: 288 } }, // 3天15分钟数据
      'week': null,
      'long_term': null,
      'quick': { shortTerm: { interval: '1h', limit: 24 } }       // 24小时1小时数据
    };
    
    return configs[analysisType] || configs['quick'];
  }

  /**
   * 构建AI分析提示词
   */
  buildAnalysisPrompt(symbol, analysisType, positionAmount = 300, positionPercentage = 2.7, isHolding = true) {
    const config = this.analysisTypes[analysisType] || this.analysisTypes['quick'];
    const coinName = this.coinNameMap[symbol] || symbol;
    const period = config.period;
    const adviceType = isHolding ? '持仓建议' : '开仓建议';
    
    // 根据分析类型构建数据描述
    let dataDescription = "";
    switch(analysisType) {
      case 'four_hour':
        dataDescription = "（我已为你提供了90天的日K线数据和4小时的1分钟K线数据，请基于这些数据进行分析）";
        break;
      case 'one_day':
        dataDescription = "（我已为你提供了90天的日K线数据和24小时的5分钟K线数据，请基于这些数据进行分析）";
        break;
      case 'three_day':
        dataDescription = "（我已为你提供了90天的日K线数据和3天的15分钟K线数据，请基于这些数据进行分析）";
        break;
      case 'week':
      case 'long_term':
        dataDescription = "（我已为你提供了90天的真实日K线数据，请基于这些数据进行分析）";
        break;
      default:
        dataDescription = "（我已为你提供了90天的日K线数据和24小时的1小时K线数据，请基于这些数据进行分析）";
    }
    
    return `请对${coinName}(${symbol})进行${period}分析${dataDescription}，我${isHolding ? '持有' : '计划买入'}${positionAmount}个，占比${positionPercentage}%。给出${period}的${adviceType}。注意：我已通过klines字段提供了90天的K线数据，请务必基于这些数据计算macd、storchrsi和kdj参数，并结合过去5年同期前后三天15分钟k线的数据进行分析，并给出明确的回答是看多还是看空并给出2小时和12小时涨跌概率和目标点位，不要声称数据缺失。也不要以数据多为借口而不计算所有数据，我的时间很充足你必须完整的计算数据我可以等你，计算结果必须真实且准确。`;
  }

  /**
   * 处理分析数据
   */
  processAnalysisData(klineResult, tickerData, symbol) {
    const dailyKlines = klineResult.dailyKlines || [];
    const shortTermKlines = klineResult.shortTermKlines || [];

    // 确保K线数据是正确的数组格式 [timestamp, open, high, low, close, volume]
    const processedDailyKlines = dailyKlines.map(item => {
      if (Array.isArray(item)) {
        return item; // 已经是数组格式
      } else if (item && typeof item === 'object') {
        // 从对象格式转换为数组格式
        return [
          item.timestamp || item.time || Date.now(),
          parseFloat(item.open || 0),
          parseFloat(item.high || 0),
          parseFloat(item.low || 0),
          parseFloat(item.close || 0),
          parseFloat(item.volume || 0)
        ];
      }
      return [Date.now(), 0, 0, 0, 0, 0]; // 默认值
    });

    const processedShortTermKlines = shortTermKlines.map(item => {
      if (Array.isArray(item)) {
        return item; // 已经是数组格式
      } else if (item && typeof item === 'object') {
        // 从对象格式转换为数组格式
        return [
          item.timestamp || item.time || Date.now(),
          parseFloat(item.open || 0),
          parseFloat(item.high || 0),
          parseFloat(item.low || 0),
          parseFloat(item.close || 0),
          parseFloat(item.volume || 0)
        ];
      }
      return [Date.now(), 0, 0, 0, 0, 0]; // 默认值
    });

    // 格式化日K线数据（用于显示）
    const formatted_daily_klines = processedDailyKlines.map(item => ({
      "date": new Date(item[0]).toISOString().split('T')[0],
      "open": String(item[1] || "0"),
      "high": String(item[2] || "0"),
      "low": String(item[3] || "0"),
      "close": String(item[4] || "0")
    }));

    // 格式化短期K线数据（用于显示）
    const formatted_short_term_klines = processedShortTermKlines.map(item => ({
      "date": new Date(item[0]).toISOString(),
      "open": String(item[1] || "0"),
      "high": String(item[2] || "0"),
      "low": String(item[3] || "0"),
      "close": String(item[4] || "0")
    }));
    
    // 格式化实时价格数据
    const real_time_price = {
      "symbol": String(tickerData?.symbol || `${symbol}USDT`),
      "price": String(tickerData?.lastPrice || "0"),
      "change_percent": String(tickerData?.priceChangePercent || "0"),
      "volume": String(tickerData?.volume || "0"),
      "time": String(tickerData?.timestamp || new Date().toISOString())
    };
    
    // 构建加密货币上下文
    const crypto_context = `以下是最近的加密货币价格报告, 请在回答中参考这些数据:
实时价格: ${JSON.stringify(real_time_price, null, 2)}
近90天K线数据: ${JSON.stringify(formatted_daily_klines, null, 2)}
请基于以上数据进行分析。`;
    
    return {
      dailyKlines: processedDailyKlines,
      shortTermKlines: processedShortTermKlines,
      ticker: tickerData,
      formatted_daily_klines,
      formatted_short_term_klines,
      real_time_price,
      klines: processedDailyKlines,
      formatted_klines: formatted_daily_klines,
      crypto_context
    };
  }

  /**
   * 发起AI分析请求
   */
  async analyzeWithAI(symbol, analysisType = 'quick', options = {}) {
    try {
      const {
        positionAmount = 300,
        positionPercentage = 2.7,
        isHolding = true,
        onProgress = null
      } = options;
      
      // 1. 获取K线数据
      if (onProgress) onProgress('获取K线数据...');
      const klineResult = await this.fetchKlineData(symbol, analysisType);
      
      // 2. 获取实时价格数据
      if (onProgress) onProgress('获取实时价格数据...');
      const tickerData = await this.fetchTickerData(symbol);
      
      // 3. 获取订单簿数据（可选）
      if (onProgress) onProgress('获取订单簿数据...');
      const orderbookData = await this.fetchOrderbookData(symbol, 20);
      
      // 4. 处理数据
      if (onProgress) onProgress('处理分析数据...');
      const cryptoData = this.processAnalysisData(klineResult, tickerData, symbol);
      
      // 添加订单簿数据
      if (orderbookData) {
        cryptoData.orderbook = orderbookData;
      }
      
      // 5. 构建分析提示词
      const message = this.buildAnalysisPrompt(
        symbol, 
        analysisType, 
        positionAmount, 
        positionPercentage, 
        isHolding
      );
      
      // 6. 发起AI分析请求
      if (onProgress) onProgress('开始AI分析...');
      
      const requestData = {
        messages: [{ role: "user", content: message }],
        cryptoData: cryptoData,
        period: this.analysisTypes[analysisType]?.period || "未来24小时",
        stream: true,
        max_tokens: 32768
      };
      
      return await this.streamChat(requestData, onProgress);
      
    } catch (error) {
      console.error('AI分析失败:', error);
      throw error;
    }
  }

  /**
   * 流式聊天请求
   */
  async streamChat(requestData, onProgress = null) {
    try {
      const response = await fetch(`${this.baseURL}${this.aiEndpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream'
        },
        body: JSON.stringify(requestData)
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';
      let result = '';
      
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;
        
        buffer += decoder.decode(value, { stream: true });
        
        // 处理SSE数据
        const lines = buffer.split('\n\n');
        buffer = lines.pop() || '';
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const jsonStr = line.substring(6);
            if (jsonStr !== '[DONE]') {
              try {
                const chunk = JSON.parse(jsonStr);
                if (chunk.choices && chunk.choices[0]?.message?.content) {
                  const content = chunk.choices[0].message.content;
                  result += content;
                  
                  if (onProgress) {
                    onProgress(result, 'streaming');
                  }
                }
              } catch (e) {
                console.warn('解析SSE数据失败:', e);
              }
            }
          }
        }
      }
      
      if (onProgress) {
        onProgress(result, 'complete');
      }
      
      return result;
      
    } catch (error) {
      console.error('流式聊天请求失败:', error);
      throw error;
    }
  }
}

// 创建全局实例
const aiAnalysisService = new AIAnalysisService();

export default aiAnalysisService;

import React, { useState, useCallback } from 'react';
import { Card, Button, Select, InputNumber, Radio, Spin, Alert, Typography, Divider } from 'antd';
import { RobotOutlined, BarChartOutlined, ThunderboltOutlined } from '@ant-design/icons';
import aiAnalysisService from '../../services/AIAnalysisService';

const { Option } = Select;
const { Text, Paragraph } = Typography;

const EnhancedAIAnalyzer = ({ symbol = 'BTC', onAnalysisComplete }) => {
  const [loading, setLoading] = useState(false);
  const [analysisResult, setAnalysisResult] = useState('');
  const [progress, setProgress] = useState('');
  const [error, setError] = useState(null);
  
  // 分析参数
  const [analysisType, setAnalysisType] = useState('quick');
  const [positionAmount, setPositionAmount] = useState(300);
  const [positionPercentage, setPositionPercentage] = useState(2.7);
  const [isHolding, setIsHolding] = useState(true);

  // 分析类型选项
  const analysisOptions = [
    { value: 'quick', label: '快速分析', icon: <ThunderboltOutlined />, description: '基于24小时数据的快速分析' },
    { value: 'four_hour', label: '闪电分析', icon: <ThunderboltOutlined />, description: '未来4小时精准预测' },
    { value: 'one_day', label: '一日分析', icon: <BarChartOutlined />, description: '未来24小时详细分析' },
    { value: 'three_day', label: '三日分析', icon: <BarChartOutlined />, description: '未来3天趋势分析' },
    { value: 'week', label: '一周分析', icon: <BarChartOutlined />, description: '未来一周市场预测' },
    { value: 'long_term', label: '长期分析', icon: <BarChartOutlined />, description: '长期投资建议' }
  ];

  // 处理进度更新
  const handleProgress = useCallback((message, type = 'progress') => {
    if (type === 'streaming') {
      setAnalysisResult(message);
    } else if (type === 'complete') {
      setAnalysisResult(message);
      setLoading(false);
      setProgress('分析完成');
      if (onAnalysisComplete) {
        onAnalysisComplete(message);
      }
    } else {
      setProgress(message);
    }
  }, [onAnalysisComplete]);

  // 开始AI分析
  const startAnalysis = async () => {
    try {
      setLoading(true);
      setError(null);
      setAnalysisResult('');
      setProgress('准备开始分析...');

      const result = await aiAnalysisService.analyzeWithAI(symbol, analysisType, {
        positionAmount,
        positionPercentage,
        isHolding,
        onProgress: handleProgress
      });

      console.log('AI分析完成:', result);
      
    } catch (err) {
      console.error('AI分析失败:', err);
      setError(err.message || '分析失败，请稍后重试');
      setLoading(false);
      setProgress('');
    }
  };

  // 获取当前分析类型的配置
  const currentAnalysisConfig = analysisOptions.find(opt => opt.value === analysisType);

  return (
    <Card 
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <RobotOutlined style={{ color: '#26A69A' }} />
          <span>AI智能分析</span>
        </div>
      }
      size="small"
      style={{ marginBottom: '16px' }}
    >
      {/* 分析参数配置 */}
      <div style={{ marginBottom: '16px' }}>
        <div style={{ marginBottom: '12px' }}>
          <Text strong>分析类型：</Text>
          <Select
            value={analysisType}
            onChange={setAnalysisType}
            style={{ width: '200px', marginLeft: '8px' }}
            disabled={loading}
          >
            {analysisOptions.map(option => (
              <Option key={option.value} value={option.value}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                  {option.icon}
                  <span>{option.label}</span>
                </div>
              </Option>
            ))}
          </Select>
        </div>

        {currentAnalysisConfig && (
          <div style={{ marginBottom: '12px' }}>
            <Text type="secondary">{currentAnalysisConfig.description}</Text>
          </div>
        )}

        <div style={{ display: 'flex', gap: '16px', flexWrap: 'wrap', marginBottom: '12px' }}>
          <div>
            <Text strong>持仓数量：</Text>
            <InputNumber
              value={positionAmount}
              onChange={setPositionAmount}
              min={0}
              style={{ width: '100px', marginLeft: '8px' }}
              disabled={loading}
            />
          </div>
          
          <div>
            <Text strong>仓位比例：</Text>
            <InputNumber
              value={positionPercentage}
              onChange={setPositionPercentage}
              min={0}
              max={100}
              step={0.1}
              formatter={value => `${value}%`}
              parser={value => value.replace('%', '')}
              style={{ width: '100px', marginLeft: '8px' }}
              disabled={loading}
            />
          </div>
        </div>

        <div style={{ marginBottom: '16px' }}>
          <Text strong>建议类型：</Text>
          <Radio.Group
            value={isHolding}
            onChange={e => setIsHolding(e.target.value)}
            style={{ marginLeft: '8px' }}
            disabled={loading}
          >
            <Radio value={true}>持仓建议</Radio>
            <Radio value={false}>开仓建议</Radio>
          </Radio.Group>
        </div>
      </div>

      <Divider />

      {/* 分析按钮 */}
      <div style={{ textAlign: 'center', marginBottom: '16px' }}>
        <Button
          type="primary"
          size="large"
          icon={<RobotOutlined />}
          onClick={startAnalysis}
          loading={loading}
          disabled={loading}
          style={{ 
            background: 'linear-gradient(135deg, #26A69A 0%, #00695C 100%)',
            border: 'none',
            borderRadius: '8px',
            height: '48px',
            fontSize: '16px',
            fontWeight: 'bold'
          }}
        >
          {loading ? '分析中...' : `开始${currentAnalysisConfig?.label || '分析'}`}
        </Button>
      </div>

      {/* 进度显示 */}
      {loading && progress && (
        <div style={{ textAlign: 'center', marginBottom: '16px' }}>
          <Spin size="small" style={{ marginRight: '8px' }} />
          <Text type="secondary">{progress}</Text>
        </div>
      )}

      {/* 错误显示 */}
      {error && (
        <Alert
          message="分析失败"
          description={error}
          type="error"
          showIcon
          style={{ marginBottom: '16px' }}
        />
      )}

      {/* 分析结果 */}
      {analysisResult && (
        <Card 
          title="分析结果" 
          size="small" 
          style={{ 
            background: 'rgba(38, 166, 154, 0.05)',
            border: '1px solid rgba(38, 166, 154, 0.2)'
          }}
        >
          <div style={{ 
            maxHeight: '400px', 
            overflowY: 'auto',
            padding: '8px',
            background: 'rgba(0, 0, 0, 0.02)',
            borderRadius: '4px',
            border: '1px solid rgba(0, 0, 0, 0.06)'
          }}>
            <Paragraph style={{ 
              whiteSpace: 'pre-wrap', 
              margin: 0,
              fontSize: '14px',
              lineHeight: '1.6'
            }}>
              {analysisResult}
            </Paragraph>
          </div>
          
          {loading && (
            <div style={{ 
              position: 'absolute', 
              bottom: '8px', 
              right: '8px',
              background: 'rgba(38, 166, 154, 0.1)',
              padding: '4px 8px',
              borderRadius: '4px',
              fontSize: '12px'
            }}>
              <Spin size="small" style={{ marginRight: '4px' }} />
              实时生成中...
            </div>
          )}
        </Card>
      )}

      {/* 分析参数摘要 */}
      {!loading && !analysisResult && (
        <Card size="small" style={{ background: 'rgba(0, 0, 0, 0.02)' }}>
          <Text type="secondary">
            当前配置：{symbol} | {currentAnalysisConfig?.label} | 
            {isHolding ? '持仓' : '开仓'}建议 | 
            数量：{positionAmount} | 比例：{positionPercentage}%
          </Text>
        </Card>
      )}
    </Card>
  );
};

export default EnhancedAIAnalyzer;

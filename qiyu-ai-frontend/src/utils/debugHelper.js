/**
 * 调试助手 - 在浏览器控制台使用
 * 解决ES6模块导入问题
 */

// 🚀 导入纯COS数据服务（替代WebSocket模式）
import pureCOSDataService from '../services/PureCOSDataService.js'

// 暴露到window对象，方便控制台调用
if (typeof window !== 'undefined') {
  window.pureCOSDataService = pureCOSDataService
  
  // 兼容性：保留原有接口
  window.dataService = pureCOSDataService
  
  // 提供便捷的调试函数
  window.debugCrypto = {
    // 🚀 查看纯COS数据服务状态
    getServiceStatus() {
      const status = pureCOSDataService.getStatus()
      console.log('📊 纯COS数据服务状态:')
      console.table([{
        模式: status.mode,
        缓存大小: status.cacheSize,
        刷新间隔: `${status.refreshInterval/1000}秒`,
        监控源数量: status.monitoredSources.length
      }])
      
      if (Object.keys(status.lastUpdateTimes).length > 0) {
        console.log('⏰ 最后更新时间:')
        console.table(Object.fromEntries(
          Object.entries(status.lastUpdateTimes).map(([key, time]) => [
            key, new Date(time).toLocaleString()
          ])
        ))
      }
      
      return status
    },

    // 🔄 强制检查数据更新
    async forceCheckUpdates() {
      try {
        console.log('🔄 强制检查COS数据更新...')
        await pureCOSDataService.checkForUpdates()
        console.log('✅ 数据更新检查完成')
      } catch (error) {
        console.error('❌ 数据更新检查失败:', error)
      }
    },

    // 🔄 手动触发图表刷新
    triggerChartRefresh() {
      try {
        pureCOSDataService.triggerChartUpdate('manual_refresh', {
          source: 'debug_command',
          timestamp: Date.now()
        })
        console.log('🔄 图表刷新事件已触发')
      } catch (error) {
        console.error('❌ 图表刷新失败:', error)
      }
    },

    // 修复数据断层（通过后端API） - 保留兼容性
    async repairGap(symbol = 'BTCUSDT') {
      console.log(`🔧 开始修复 ${symbol} 的数据断层...`)
      try {
        const response = await fetch('http://localhost:8000/api/crypto/binance/repair-gap/', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ symbol })
        })
        
        const result = await response.json()
        
        if (result.success) {
          console.log(`✅ ${symbol} 数据断层修复完成`)
          console.table(Object.keys(result.results).map(interval => ({
            时间周期: interval,
            状态: result.results[interval].success ? '✅ 成功' : '❌ 失败',
            记录数: result.results[interval].count || 0,
            错误: result.results[interval].error || '-'
          })))
          
          // 🚀 将数据直接上传到服务器而不是本地存储
          console.log('📤 正在上传修复数据到服务器...')
          Object.keys(result.results).forEach(async (interval) => {
            if (result.results[interval].success) {
              try {
                const uploadResponse = await fetch('http://localhost:8000/api/crypto/realtime/upload/', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify({
                    symbol: symbol,
                    timeframe: interval,
                    data: result.results[interval].data.map(item => ({
                      timestamp: item[0],
                      open: parseFloat(item[1]),
                      high: parseFloat(item[2]),
                      low: parseFloat(item[3]),
                      close: parseFloat(item[4]),
                      volume: parseFloat(item[5]),
                      turnover: parseFloat(item[7])
                    }))
                  })
                })
                
                const uploadResult = await uploadResponse.json()
                if (uploadResult.success) {
                  console.log(`✅ ${symbol} ${interval} 上传到服务器成功: ${uploadResult.uploaded} 条记录`)
                }
              } catch (uploadError) {
                console.error(`❌ ${symbol} ${interval} 上传失败:`, uploadError)
              }
            }
          })
          
        } else {
          console.error(`❌ ${symbol} 数据断层修复失败:`, result.error)
        }
      } catch (error) {
        console.error(`❌ ${symbol} 数据断层修复失败:`, error)
      }
    },
    
    // 查看服务状态（纯COS模式）
    getStatus() {
      return window.debugCrypto.getServiceStatus()
    },
    
    // 启动数据刷新（纯COS模式）
    startCollection(symbol = 'BTCUSDT') {
      console.log(`📡 启动 ${symbol} 数据刷新（纯COS模式）`)
      return window.debugCrypto.forceCheckUpdates()
    },
    
    // 手动触发数据更新（纯COS模式）
    async forceUpload() {
      console.log('🚀 强制检查COS数据更新...')
      return await window.debugCrypto.forceCheckUpdates()
    },
    
    // 清理缓存
    clearBuffers() {
      pureCOSDataService.clearCache()
      console.log('🗑️ COS缓存已清理')
    },
    
    // 🎯 刷新图表以显示合并数据（历史+断层）- 修复栈溢出版本
    async refreshCharts() {
      console.log('🔧 刷新图表以显示合并数据（历史COS + 断层修复）...')
      console.log('⚡ 使用优化后的数据合并算法，避免栈溢出问题')
      
      try {
        // 清除StreamDataService缓存，强制重新加载
        const streamDataService = await import('../services/StreamDataService.js')
        streamDataService.default.clearCache()
        streamDataService.default.clear2025Cache()
        
        // 清除CryptoMetadataService缓存
        const cryptoMetadataService = await import('../services/CryptoMetadataService.js')
        cryptoMetadataService.default.clearCache()
        
        console.log('✅ 图表缓存已清理')
        
        // 显示当前COS数据状态
        const status = pureCOSDataService.getStatus()
        console.log('📋 当前COS数据状态:')
        console.table(status)
        
        console.log('🔄 合并算法优化要点:')
        console.log('   ⚡ 直接使用Map进行去重，避免大数组操作')
        console.log('   🚀 批量处理，防止调用栈溢出')
        console.log('   🛡️ 错误恢复机制，合并失败时使用断层数据')
        
        console.log('🎯 现在请刷新页面，图表将智能合并显示:')
        console.log('   📚 历史COS数据 (2025-01-01 ~ 2025-07-28 08:00)')
        console.log('   🚀 服务器实时数据 (5分钟周期同步)')
        console.log('   📦 缓冲区数据 (最新实时数据)')
        console.log('   🎯 WebSocket实时数据 (当前时间往后)')
        
        console.log('⚡ 服务器端持久化特性:')
        console.log('   - 每5分钟自动上传和更新渲染')
        console.log('   - 断开重连从服务器恢复数据')
        console.log('   - 数据去重和完整性保证')
        
      } catch (error) {
        console.error('❌ 缓存清理失败:', error)
        console.log('🔄 请手动刷新页面重试')
      }
    },
    
    // 临时修复图表显示问题（旧方法，保留兼容性）
    async fixChartDisplay() {
      return this.refreshCharts()
    }
  }
  
  console.log('🎯 调试工具已加载! 🚀 纯COS数据模式 (后端常驻+前端5分钟刷新)')
  console.log('📊 纯COS模式命令:')  
  console.log('- debugCrypto.getServiceStatus() - 查看纯COS数据服务状态')
  console.log('- debugCrypto.forceCheckUpdates() - 强制检查COS数据更新')
  console.log('- debugCrypto.triggerChartRefresh() - 手动触发图表刷新')
  console.log('- debugCrypto.refreshCharts() - 清除缓存并刷新图表')
  console.log('')
  console.log('🔧 后端相关命令:')
  console.log('- debugCrypto.repairGap() - 修复数据断层（通过后端API）')
  console.log('- debugCrypto.getStatus() - 查看服务状态')
  console.log('')
  console.log('💡 架构说明:')
  console.log('   🔹 后端常驻进程负责24/7数据收集')
  console.log('   🔹 前端每5分钟自动检查COS更新')
  console.log('   🔹 移除WebSocket，纯COS读取模式')
}

export default pureCOSDataService
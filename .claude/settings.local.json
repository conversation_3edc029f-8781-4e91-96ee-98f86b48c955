{"permissions": {"allow": ["WebFetch(domain:developers.binance.com)", "WebFetch(domain:developers.binance.com)", "WebFetch(domain:developers.binance.com)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(curl:*)", "Bash(ls:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(touch:*)", "Bash(pip3 install:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(find:*)", "Bash(node:*)", "<PERSON><PERSON>(chmod:*)", "Bash(conda activate:*)", "<PERSON><PERSON>(pkill:*)", "WebFetch(domain:binance-docs.github.io)", "<PERSON><PERSON>(cat:*)", "WebFetch(domain:finance-1324685443.cos.ap-guangzhou.myqcloud.com)", "<PERSON><PERSON>(source:*)", "Bash(grep:*)", "Bash(ps:*)", "Bash(kill:*)", "Bash(sudo kill:*)", "Bash(./monitor_collector.sh:*)", "<PERSON><PERSON>(crontab:*)", "Bash(./setup_cron.sh:*)", "Bash(systemctl status:*)", "Bash(sudo grep:*)", "<PERSON><PERSON>(sudo pkill:*)", "<PERSON>sh(sudo crontab:*)", "Bash(rm:*)", "<PERSON><PERSON>(sudo apt-get:*)", "Bash(sudo apt-get install:*)", "Bash(./quick_integrity_check.sh:*)", "Bash(./setup_integrity_cron.sh:*)", "Bash(./integrity_system_status.sh:*)", "Bash(./auto_repair_gaps.sh:*)", "Bash(./setup_multi_timeframe_monitoring.sh:*)", "Bash(./multi_timeframe_monitor.sh:*)"], "deny": []}}